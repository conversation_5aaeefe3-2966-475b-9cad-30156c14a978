# DataBuff 报告模板系统增强 PRD v2.0 - Claude版本

> **版本**：v2.0.0-claude
> **撰写日期**：2025-07-28
> **适用产品线**：Ultra Lens（可编辑）、Holo Lens（只读）
> **说明**：本文档基于原需求重新梳理，重点澄清异常检测与告警系统的差异，并完善AI功能控制方案。

---

## 1. 背景与核心理念

### 1.1 业务背景

现有弹窗式模板编辑器交互层级深、组件类型单一，且缺乏智能化的数据洞察能力。为提高运维/研发生成报告的效率与洞察深度，本期迭代聚焦以下三个核心改进：

1. **编辑体验现代化**——从"弹窗编辑"升级到"整页编辑"，提供所见即所得的直观体验
2. **组件体系完整化**——新增标题、正文、异常评估三类组件，覆盖完整的报告编写场景
3. **智能化能力增强**——集成异常识别与AI洞察，让报告具备主动发现问题的能力

### 1.2 异常检测 vs 告警系统的概念澄清

**重要概念区分**：

- **告警系统**：持续监控指标，当触发条件时主动发送通知（邮件、钉钉等），目的是**及时响应问题**
- **异常检测（本需求）**：在生成报告时对指标数据进行回顾性分析，判断历史时间段内是否存在异常模式，目的是**总结分析问题**

两者虽然使用相似的算法（阈值检测、动态基线），但应用场景完全不同：

| 维度 | 告警系统 | 异常检测（报告功能） |
|------|----------|---------------------|
| **时效性** | 实时/准实时 | 批处理回顾分析 |
| **目的** | 即时响应问题 | 历史问题总结 |
| **输出** | 告警通知 | 异常描述文本 |
| **数据源** | 实时指标流 | 历史时间段数据 |
| **处理方式** | 流式处理 | 批量分析处理 |

### 1.3 智能化分析理念

**AI见解的价值定位**：
- 不是简单的数据可视化，而是对数据背后**业务含义的解读**
- 基于历史模式识别**潜在问题和优化机会**
- 提供**可操作的建议**，而非泛泛的描述

---

## 2. 范围与目标

| 模块 | 目标 | 变更类型 | 价值 |
|------|------|----------|------|
| **模板编辑器** | 弹窗 → 整页，支持拖拽与自动编号 | **体验改进** | 提升编辑效率50% |
| **组件库** | 新增标题、正文、异常评估组件 | **功能扩展** | 覆盖完整报告场景 |
| **异常识别** | 集成阈值/动态基线检测能力 | **智能化** | 主动发现历史异常 |
| **AI洞察** | OpenAI集成的图表分析与建议 | **智能化** | 深度业务洞察 |
| **报告生成** | 支持混合组件的Word输出 | **功能增强** | 一键生成专业报告 |

---

## 3. 功能需求详述

### 3.1 整页式模板编辑器

#### 3.1.1 界面升级

| 功能点 | 描述 | 交互要点 | Ultra-Lens | Holo-Lens |
|--------|------|----------|------------|-----------|
| **编辑页面** | 路由：`/template/edit/<id>` | 编辑区≥90% viewport | ✓ | 只读预览 |
| **实时保存** | 保存即生效，版本号沿用现有逻辑 | 离开页面前未保存二次确认 | ✓ | × |
| **拖拽排序** | 组件间可任意拖拽调整顺序 | 拖拽反馈、撤销/重做 | ✓ | × |

#### 3.1.2 兼容性保障

- **向后兼容**：现有模板无需迁移，自动识别并正常展示
- **渐进升级**：用户可选择性使用新组件，不强制全部替换
- **数据一致性**：新旧格式数据在同一content字段中共存

### 3.2 新增组件类型

#### 3.2.1 标题组件

**功能特性**：
- **三级层次**：大标题（一、二、三）、中标题（1.1、1.2）、小标题（1.1.1、1.1.2）
- **自动编号**：系统根据组件顺序和层级自动生成编号，用户不可手动修改
- **目录生成**：Word导出时自动生成带书签的目录结构

| 属性 | 配置项 | 限制 | 示例 |
|------|--------|------|------|
| **标题内容** | content | ≤50字 | "系统性能分析总结" |
| **层级类型** | level | 1-3 | 1=大标题，2=中标题，3=小标题 |
| **字体样式** | titleType | large/medium/small | 对应18号/16号/14号字体 |

#### 3.2.2 正文组件

**功能特性**：
- **富文本编辑**：支持基础格式（粗体、斜体、下划线、列表）
- **字数控制**：实时字数统计，接近限制时预警
- **HTML清理**：自动过滤危险标签，保留安全格式

| 属性 | 配置项 | 限制 | 说明 |
|------|--------|------|------|
| **内容类型** | contentType | html/plain | 富文本或纯文本 |
| **字数限制** | wordLimit | 10-2000字 | 可配置，默认1000字 |
| **当前字数** | currentWordCount | 只读显示 | 实时计算（去除HTML标签） |

#### 3.2.3 异常评估组件

**核心价值**：自动汇总报告中所有启用异常检测的图表的异常发现

**工作机制**：
1. **扫描模板**：找出所有配置了异常检测的图表组件
2. **回顾分析**：对报告时间范围内的指标数据进行异常检测
3. **结果汇总**：按照预定义模板格式化异常描述
4. **智能显示**：有异常时显示详情，无异常时显示"本次报告周期内无异常"

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| **异常模板** | 格式：`{system}系统的{service}服务的{metric}指标在{time}开始异常，持续{duration}分钟` | 可自定义 |
| **无异常文案** | 当未检测到异常时显示的文本 | "本次报告周期内无异常" |
| **自动生成** | 是否自动收集异常数据 | true |

### 3.3 图表组件高级配置

#### 3.3.1 异常检测配置

**重要说明**：此功能用于报告生成时的**历史数据回顾分析**，不产生实时告警

**检测方法**：

1. **阈值检测**
   - 用户设定上下阈值
   - 系统分析报告时间段内指标值是否超出范围
   - 适用于已知正常范围的指标

2. **动态基线检测**
   - 系统基于历史7天数据自动计算基线
   - 基线算法：`99分位值 + (75分位值 - 25分位值) × 10`
   - 适用于动态变化但有规律的指标

**配置界面**：

| 配置项 | 类型 | 说明 | 验证规则 |
|--------|------|------|----------|
| **启用异常检测** | 复选框 | 开启后出现检测方法选项 | 必选 |
| **检测方法** | 下拉选择 | 阈值 / 动态基线 | 二选一必填 |
| **阈值设置** | 数值输入 | 当选择阈值检测时出现 | 非空校验，前后端双重验证 |
| **基线参数** | 自动计算 | 当选择动态基线时无需输入 | 系统自动处理 |

**数据质量要求**：
- **阈值检测**：无特殊要求，有数据即可检测
- **动态基线检测**：需要至少2016个历史数据点才能进行有效检测

#### 3.3.2 AI见解与建议

**功能控制机制**：

```javascript
// 前端控制逻辑示例
const checkAIAvailability = async () => {
    const aiConfig = await fetch('/api/ai/getAIConfig').then(res => res.json());
    const isAIEnabled = aiConfig.data?.open === true;
    
    // 控制选项状态
    const aiInsightOption = document.getElementById('ai-insight-checkbox');
    if (isAIEnabled) {
        aiInsightOption.disabled = false;
        aiInsightOption.title = '';
    } else {
        aiInsightOption.disabled = true;
        aiInsightOption.title = '启用外接大模型功能后可用';
        aiInsightOption.style.color = '#ccc';
    }
};
```

**AI分析内容**：

1. **AI分析洞察**：
   - 数据趋势识别（上升/下降/周期性）
   - 异常点解释
   - 业务影响分析

2. **AI优化建议**：
   - 性能优化方向
   - 容量规划建议  
   - 运维改进建议

**限制条件**：
- **图表限制**：每个图表仅支持单一指标，不支持多指标或公式计算
- **依赖配置**：需要在系统设置中配置OpenAI API Key和模型
- **产品差异**：Ultra Lens可配置和使用，Holo Lens仅显示已生成的内容

### 3.4 报告生成与渲染

#### 3.4.1 生成流程

```mermaid
flowchart TD
    A[定时扫描待生成报告] -->|每5分钟| B[解析模板组件]
    B --> C{组件类型识别}
    C -->|标题| D[自动编号处理]
    C -->|正文| E[富文本转换]
    C -->|图表| F[数据查询]
    C -->|异常评估| G[异常数据收集]
    
    F --> H[异常检测分析]
    F --> I[AI洞察生成]
    G --> J[异常描述格式化]
    
    D --> K[Word文档组装]
    E --> K
    H --> K
    I --> K
    J --> K
    
    K --> L[文件保存与下载]
```

#### 3.4.2 异常检测处理流程

**区别于告警系统的处理方式**：

1. **数据获取**：查询报告时间范围内的完整历史数据
2. **批量分析**：一次性处理整个时间段，而非实时流式处理
3. **结果输出**：生成文本描述用于报告，而非发送告警通知
4. **错误处理**：检测失败时在报告中显示"检测暂时不可用"，不影响整体报告生成

#### 3.4.3 AI集成处理

**超时与降级策略**：
- **生成超时**：AI分析限制10秒超时
- **失败降级**：API调用失败时显示"AI洞察暂时不可用"
- **配置检查**：优先检查AI功能是否启用，避免无效调用

---

## 4. 技术实现要点

### 4.1 现有系统集成

**复用现有服务**：
- **OpenAI服务**：复用 `com.databuff.service.root.service.OpenAIService.sendMessage()` 方法
- **动态基线算法**：复用 `com.databuff.metric.impl.metric.DefSingleMetricAggregator.baselineResult()` 方法
- **AI配置管理**：使用现有 `dc_databuff_ai_config` 表和 `AIService`

**API兼容性**：
- 保持现有报告模板API不变
- 新组件配置存储在现有 `content` 字段的 `extra` 属性中
- 向后兼容现有模板数据格式

### 4.2 前端状态控制

**AI功能状态检测**：
```javascript
// 页面加载时检测AI功能状态
const initAIFeatureState = async () => {
    try {
        const response = await fetch('/api/ai/getAIConfig');
        const aiConfig = await response.json();
        const isAIEnabled = aiConfig.data?.open === true;
        
        // 更新UI状态
        updateAIFeatureUI(isAIEnabled);
    } catch (error) {
        console.error('AI配置检测失败:', error);
        updateAIFeatureUI(false);
    }
};

const updateAIFeatureUI = (enabled) => {
    const aiCheckbox = document.querySelector('#ai-insights-checkbox');
    if (enabled) {
        aiCheckbox.disabled = false;
        aiCheckbox.parentElement.classList.remove('disabled');
    } else {
        aiCheckbox.disabled = true;
        aiCheckbox.checked = false;
        aiCheckbox.parentElement.classList.add('disabled');
        aiCheckbox.setAttribute('title', '启用外接大模型功能后可用');
    }
};
```

### 4.3 数据存储方案

**content字段扩展格式**：
```json
{
  "index": 0,
  "type": "title",
  "title": "标题显示文本",
  "text": "",
  "query": "",
  "extra": "{
    \"version\": \"2.0\",
    \"type\": \"title\",
    \"content\": \"实际标题内容\",
    \"titleType\": \"large\",
    \"level\": 1,
    \"autoNumber\": true
  }"
}
```

---

## 5. 业务流程图

### 5.1 整体流程

```mermaid
flowchart TD
    A[用户进入整页编辑器] --> B[检查AI功能状态]
    B --> C[拖拽添加组件]
    C --> D{组件类型}
    
    D -->|标题/正文| E[配置基础属性]
    D -->|图表| F[配置图表参数]
    D -->|异常评估| G[自动配置]
    
    F --> H{是否配置异常检测?}
    H -->|是| I[选择检测方法]
    H -->|否| J[配置AI见解]
    
    I --> K[阈值输入 / 动态基线自动]
    J --> L{AI功能是否启用?}
    L -->|是| M[可选择AI见解]
    L -->|否| N[显示灰色禁用状态]
    
    E --> O[保存模板]
    G --> O
    K --> O
    M --> O
    N --> O
    
    O --> P[定时报告生成]
    P --> Q[异常检测 + AI分析]
    Q --> R[Word报告输出]
```

### 5.2 异常检测专项流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant T as 模板编辑器
    participant R as 报告生成器
    participant A as 异常检测引擎
    participant D as 数据库/TSDB
    participant AI as AI服务
    
    U->>T: 配置图表异常检测
    T->>T: 保存检测配置到extra字段
    
    Note over R: 定时任务触发(每5分钟)
    R->>R: 扫描待生成报告
    R->>R: 解析模板components
    
    loop 每个启用异常检测的图表
        R->>D: 查询历史数据
        D-->>R: 返回时间序列数据
        R->>A: 调用检测算法
        A-->>R: 返回异常判断结果
    end
    
    R->>R: 汇总异常结果
    R->>AI: 生成AI洞察(如果启用)
    AI-->>R: 返回分析内容
    R->>R: 组装Word文档
    R-->>U: 报告生成完成
```

---

## 6. 出界范围

### 6.1 明确不包含的功能

* **实时告警功能**：异常检测仅用于报告生成，不产生告警通知
* **多指标图表**：图表组件限制单一指标，不支持复合计算
* **模板版本管理**：复制/克隆/版本历史功能
* **自定义AI模型**：仅支持OpenAI兼容接口，不支持本地模型
* **图片/PDF输出**：仅支持Word格式报告
* **实时预览**：编辑器中的预览仅为静态展示

### 6.2 技术限制

* **动态基线质量要求**：少于2016个数据点时无法进行有效基线检测
* **AI分析超时限制**：单次AI分析限制10秒，超时则显示降级内容
* **并发生成限制**：报告生成采用队列方式，不支持大规模并发

---

## 7. 验收标准

### 7.1 功能验收

- [ ] **编辑器升级**：整页编辑界面流畅，支持组件拖拽排序
- [ ] **新组件功能**：标题自动编号、正文富文本、异常评估聚合均正常工作
- [ ] **异常检测**：阈值和动态基线检测能正确识别历史异常，结果准确
- [ ] **AI功能控制**：根据`dc_databuff_ai_config.open`字段正确控制前端选项状态
- [ ] **Word生成**：混合组件的报告能正确生成并包含所有内容

### 7.2 性能验收

- [ ] **报告生成时间**：单个报告生成时间不超过2分钟
- [ ] **AI响应时间**：AI洞察生成不超过10秒
- [ ] **页面加载时间**：编辑器页面首次加载不超过3秒

### 7.3 兼容性验收

- [ ] **数据兼容**：现有模板无需迁移即可正常使用
- [ ] **API兼容**：现有报告相关API保持向后兼容
- [ ] **浏览器兼容**：支持Chrome 80+、Firefox 75+、Safari 13+

---

**结语**：本增强方案在保持系统稳定性的前提下，通过智能化手段大幅提升报告的价值密度和用户体验，实现从"数据展示"到"洞察分析"的跃升。